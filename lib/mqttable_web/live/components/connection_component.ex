defmodule MqttableWeb.ConnectionComponent do
  @moduledoc """
  LiveComponent for handling connection-related functionality.
  This component manages the connection form and related operations.
  """
  use MqttableWeb, :live_component

  @default_ports %{
    "mqtt" => "1883",
    "mqtts" => "1884",
    "ws" => "8083",
    "wss" => "8084",
    "quic" => "4567"
  }

  @impl true
  def render(assigns) do
    # Ensure active_connection_set is assigned, even if it's nil
    assigns = assign_new(assigns, :active_connection_set, fn -> nil end)

    # Ensure connection lists are assigned, even if they're empty
    assigns = assign_new(assigns, :connected_connections, fn -> [] end)
    assigns = assign_new(assigns, :disconnected_connections, fn -> [] end)
    assigns = assign_new(assigns, :reconnecting_connections, fn -> [] end)

    # Ensure expanded_sets is assigned, even if it's empty
    assigns = assign_new(assigns, :expanded_sets, fn -> %{} end)

    # Ensure connection_sets is assigned, even if it's empty
    assigns = assign_new(assigns, :connection_sets, fn -> [] end)

    # Get connection table state from expanded_sets
    # Use broker name as the key
    connection_table_key =
      if assigns.active_connection_set do
        assigns.active_connection_set.name
      else
        nil
      end

    # Support three states: "collapsed", "semi", "expanded"
    # Default to "expanded" for backward compatibility
    connection_table_state =
      if connection_table_key do
        case Map.get(assigns.expanded_sets, connection_table_key, "expanded") do
          # Handle legacy boolean values for backward compatibility
          true -> "expanded"
          false -> "collapsed"
          # Handle new string states
          state when state in ["collapsed", "semi", "expanded"] -> state
          # Default fallback
          _ -> "expanded"
        end
      else
        "expanded"
      end

    assigns = assign(assigns, :connection_table_state, connection_table_state)

    # Sort all connections by connection_time (oldest first)
    # Put connections with nil connection_time at the end
    all_connections =
      assigns.connected_connections ++
        assigns.disconnected_connections ++ assigns.reconnecting_connections

    sorted_connections =
      all_connections
      |> Enum.sort_by(
        fn conn ->
          case conn.connection_time do
            # Far future date for nil values
            nil ->
              DateTime.from_unix!(253_402_300_799)

            # Parse string datetime to DateTime struct if it's a string
            time when is_binary(time) ->
              case DateTime.from_iso8601(time) do
                {:ok, datetime, _offset} -> datetime
                # Use far future as fallback
                _error -> DateTime.from_unix!(253_402_300_799)
              end

            # Use the DateTime directly if it's already a struct
            %DateTime{} = time ->
              time

            # Fallback for any other unexpected format
            _other ->
              DateTime.from_unix!(253_402_300_799)
          end
        end,
        DateTime
      )

    assigns = assign(assigns, :sorted_connections, sorted_connections)

    ~H"""
    <div>
      <%= if @active_connection_set do %>
        <div class="bg-base-100 p-2">
          <div>
            <div class="mt-1">
              
    <!-- Connection Table with Collapsible Feature -->
              <div
                id="connection-table-container"
                class="bg-base-100 border border-base-300 rounded-lg connection-table-container"
                phx-hook="ConnectionTableAutoAnimate"
              >
                <!-- Header with three-state controls -->
                <div class="flex items-center font-medium p-4 hover:bg-base-200 transition-colors">
                  <div class="flex items-center">
                    <svg
                      t="1751936973109"
                      class="icon"
                      viewBox="0 0 1024 1024"
                      version="1.1"
                      xmlns="http://www.w3.org/2000/svg"
                      p-id="4933"
                      width="20"
                      height="20"
                    >
                      <path
                        d="M184.795429 555.958857H182.857143v251.757714c0 18.176 14.921143 33.097143 33.097143 33.097143h259.218285c-2.230857-157.330286-131.401143-284.854857-290.377142-284.854857z m0-207.652571H182.857143v107.081143c213.796571 0.950857 387.657143 172.946286 389.997714 385.426285h110.957714c-2.011429-271.872-224.914286-492.434286-499.090285-492.434285l0.073143-0.073143zM841.142857 808.009143v-227.364572A709.083429 709.083429 0 0 0 425.874286 182.857143h-209.92A33.243429 33.243429 0 0 0 182.857143 215.954286v32.109714C511.853714 249.051429 779.483429 514.121143 781.421714 841.142857h26.624a33.243429 33.243429 0 0 0 33.097143-33.097143zM754.541714 274.724571c30.464 30.464 61.586286 69.12 86.601143 103.131429V215.661714A32.694857 32.694857 0 0 0 808.374857 182.857143h-167.058286c38.912 26.953143 79.140571 57.746286 113.225143 91.830857z"
                        fill="#9828AC"
                        p-id="4934"
                      >
                      </path>
                    </svg>
                    <div class="w-2"></div>
                    <span
                      class="cursor-pointer hover:text-primary transition-colors opacity-70"
                      phx-click="open_edit_connection_set_modal"
                      phx-value-name={@active_connection_set.name}
                      title="Click to edit broker settings"
                    >
                      {@active_connection_set.name}
                    </span>
                  </div>
                  <div class="divider divider-horizontal"></div>
                  <span
                    class="cursor-pointer hover:text-primary transition-colors opacity-70"
                    phx-click="open_edit_connection_set_modal"
                    phx-value-name={@active_connection_set.name}
                    title="Click to edit broker settings"
                  >
                    {@active_connection_set.protocol}://{@active_connection_set.host}:{@active_connection_set.port}
                  </span>
                  <div class="ml-auto flex items-center gap-2">
                    <div class="text-xs opacity-70">
                      {length(@connected_connections)}/{length(@sorted_connections)} connections
                    </div>
                    <!-- Three-state control icons -->
                    <div class="flex items-center gap-1 ml-2">
                      <!-- Full collapse icon -->
                      <button
                        class={[
                          "btn btn-xs btn-ghost p-1 h-6 w-6 min-h-0 tooltip",
                          if(@connection_table_state == "collapsed", do: "btn-active", else: "")
                        ]}
                        data-tip="Hide all connections"
                        phx-click="set_connection_table_state"
                        phx-value-state="collapsed"
                        phx-target={@myself}
                      >
                        <.icon name="hero-eye-slash" class="size-3" />
                      </button>
                      <!-- Half collapse icon (connected only) -->
                      <button
                        class={[
                          "btn btn-xs btn-ghost p-1 h-6 w-6 min-h-0 tooltip",
                          if(@connection_table_state == "semi", do: "btn-active", else: "")
                        ]}
                        data-tip="Show connected only"
                        phx-click="set_connection_table_state"
                        phx-value-state="semi"
                        phx-target={@myself}
                      >
                        <.icon name="hero-eye" class="size-3" />
                      </button>
                      <!-- Full expand icon -->
                      <button
                        class={[
                          "btn btn-xs btn-ghost p-1 h-6 w-6 min-h-0 tooltip",
                          if(@connection_table_state == "expanded", do: "btn-active", else: "")
                        ]}
                        data-tip="Show all connections"
                        phx-click="set_connection_table_state"
                        phx-value-state="expanded"
                        phx-target={@myself}
                      >
                        <.icon name="hero-squares-2x2" class="size-3" />
                      </button>
                    </div>
                  </div>
                </div>
                
    <!-- Content area based on state -->
                <%= if @connection_table_state != "collapsed" do %>
                  <div class="border-t border-base-300">
                    <div class="bg-base-100">
                      <table class="table table-zebra w-full table-with-dividers">
                        <thead>
                          <tr>
                            <th class="w-1/8 whitespace-nowrap">Name</th>
                            <th class="w-1/8 whitespace-nowrap">Client ID</th>
                            <th class="w-1/10 whitespace-nowrap">Status</th>
                            <th class="w-1/7 whitespace-nowrap">Keepalive | Clean Start</th>
                            <th class="w-1/5">Subscriptions</th>
                            <th class="w-1/5">Scheduled Messages</th>
                            <th class="w-1/9 whitespace-nowrap">Connection Time</th>
                          </tr>
                        </thead>
                        <tbody>
                          <% # Determine which connections to show based on state
                          connections_to_show =
                            case @connection_table_state do
                              "semi" ->
                                # For semi state, show only connected connections sorted by connection time
                                @connected_connections
                                |> Enum.sort_by(
                                  fn conn ->
                                    case conn.connection_time do
                                      nil ->
                                        DateTime.from_unix!(253_402_300_799)

                                      time when is_binary(time) ->
                                        case DateTime.from_iso8601(time) do
                                          {:ok, datetime, _offset} -> datetime
                                          _error -> DateTime.from_unix!(253_402_300_799)
                                        end

                                      %DateTime{} = time ->
                                        time

                                      _other ->
                                        DateTime.from_unix!(253_402_300_799)
                                    end
                                  end,
                                  DateTime
                                )

                              "expanded" ->
                                @sorted_connections

                              _ ->
                                []
                            end %>
                          <%= for conn <- connections_to_show do %>
                            <tr class="table-row-hover cursor-pointer">
                              <!-- Name with Edit Functionality -->
                              <td class="px-2 whitespace-nowrap">
                                <div class="group hover:bg-base-200 rounded-md">
                                  <div class="flex items-center py-1 px-2 w-full relative connection-item">
                                    <div
                                      class="flex items-center flex-grow cursor-pointer"
                                      phx-click="open_edit_connection_modal"
                                      phx-value-set_name={@active_connection_set.name}
                                      phx-value-client_id={conn.client_id}
                                    >
                                      <div class="relative h-5 w-5 mr-2">
                                        <img
                                          src="/images/device.svg"
                                          class="h-5 w-5 text-sm text-gray-500"
                                          alt="Device"
                                        />
                                      </div>
                                      <span class="text-sm font-medium">
                                        {String.slice(conn.name || "Unnamed", 0, 20) <>
                                          if String.length(conn.name || "Unnamed") > 20,
                                            do: "...",
                                            else: ""}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </td>
                              
    <!-- Client ID -->
                              <td class="px-2 whitespace-nowrap">
                                <span class="text-sm text-gray-600">
                                  {String.slice(conn.client_id, 0, 20) <>
                                    if String.length(conn.client_id) > 20, do: "...", else: ""}
                                </span>
                              </td>
                              
    <!-- Status with Swap Component -->
                              <td class="px-2 whitespace-nowrap">
                                <div class="flex items-center">
                                  <%= case conn.status do %>
                                    <% "connected" -> %>
                                      <label class="swap">
                                        <input
                                          type="checkbox"
                                          checked
                                          phx-click="update_connection_status"
                                          phx-value-client_id={conn.client_id}
                                          phx-value-new_status="disconnected"
                                        />
                                        <div class="swap-on badge badge-success gap-1">
                                          <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                          Connected
                                        </div>
                                        <div class="swap-off badge badge-error gap-1">
                                          <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                                          Disconnect
                                        </div>
                                      </label>
                                    <% "disconnected" -> %>
                                      <label class="swap">
                                        <input
                                          type="checkbox"
                                          phx-click="update_connection_status"
                                          phx-value-client_id={conn.client_id}
                                          phx-value-new_status="connected"
                                        />
                                        <div class="swap-on badge badge-success gap-1">
                                          <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                          Connected
                                        </div>
                                        <div class="swap-off badge badge-error gap-1">
                                          <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                                          Disconnect
                                        </div>
                                      </label>
                                    <% "reconnecting" -> %>
                                      <label class="swap">
                                        <input
                                          type="checkbox"
                                          phx-click="update_connection_status"
                                          phx-value-client_id={conn.client_id}
                                          phx-value-new_status="connected"
                                        />
                                        <div class="swap-on badge badge-success gap-1">
                                          <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                          Connected
                                        </div>
                                        <div class="swap-off badge badge-warning gap-1">
                                          <span class="w-2 h-2 bg-yellow-500 rounded-full"></span>
                                          Reconnect
                                        </div>
                                      </label>
                                    <% _ -> %>
                                      <label class="swap">
                                        <input
                                          type="checkbox"
                                          phx-click="update_connection_status"
                                          phx-value-client_id={conn.client_id}
                                          phx-value-new_status="connected"
                                        />
                                        <div class="swap-on badge badge-success gap-1">
                                          <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                          Connected
                                        </div>
                                        <div class="swap-off badge badge-error gap-1">
                                          <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                                          Disconnect
                                        </div>
                                      </label>
                                  <% end %>
                                </div>
                              </td>
                              
    <!-- MQTT Info (Combined) -->
                              <td class="px-2 whitespace-nowrap">
                                <div class="flex items-center gap-1 flex-wrap">
                                  <!-- MQTT Version Badge -->
                                  <% {version_text, _version_color} =
                                    format_mqtt_version(conn.mqtt_version || "5.0") %>
                                  <div class="badge badge-sm badge-outline gap-1">
                                    <span class="text-xs">{version_text}</span>
                                  </div>
                                  
    <!-- Keepalive Badge -->
                                  <div class="badge badge-sm badge-outline gap-1">
                                    <span>⏱️</span>
                                    <span class="text-xs">{conn.keep_alive || 60}s</span>
                                  </div>
                                  
    <!-- Clean Start Badge -->
                                  <div class="badge badge-sm badge-outline gap-1">
                                    <span>
                                      {if conn.clean_start == true or conn.clean_start == "true",
                                        do: "✅",
                                        else: "🔄"}
                                    </span>
                                  </div>
                                </div>
                              </td>
                              
    <!-- Subscriptions -->
                              <td class="px-2 w-full">
                                <div class="flex justify-between items-start">
                                  <div class="flex-grow">
                                    <%= if conn.topics && length(conn.topics) > 0 do %>
                                      <div class="flex flex-wrap gap-1">
                                        <%= for {topic, topic_index} <- Enum.with_index(conn.topics) do %>
                                          <%= case topic do %>
                                            <% %{topic: topic_str, qos: qos, nl: nl, rap: rap} -> %>
                                              <div class="inline-flex items-center gap-1 bg-base-200 hover:bg-base-300 transition-colors rounded-md px-2 py-1 text-xs group">
                                                <span
                                                  class="font-medium max-w-24 truncate cursor-pointer hover:text-primary"
                                                  title={topic_str}
                                                  phx-click="edit_subscription"
                                                  phx-value-client_id={conn.client_id}
                                                  phx-value-topic={topic_str}
                                                  phx-value-qos={qos}
                                                  phx-value-nl={nl}
                                                  phx-value-rap={rap}
                                                  phx-value-rh={Map.get(topic, :rh, 0)}
                                                  phx-value-sub_id={Map.get(topic, :id, "")}
                                                  phx-value-index={topic_index}
                                                >
                                                  {topic_str}
                                                </span>
                                                <%= if qos != 0 do %>
                                                  <span class="badge badge-xs badge-primary">
                                                    {get_qos_badge(qos)}
                                                  </span>
                                                <% end %>
                                                <%= if nl == true or nl == 1 do %>
                                                  <span class="badge badge-xs badge-info">NL</span>
                                                <% end %>
                                                <%= if rap == true or rap == 1 do %>
                                                  <span class="badge badge-xs badge-warning">
                                                    RAP
                                                  </span>
                                                <% end %>
                                                <%= if Map.has_key?(topic, :rh) && Map.get(topic, :rh, 0) != 0 do %>
                                                  <span class="badge badge-xs badge-secondary">
                                                    {get_rh_badge(Map.get(topic, :rh, 0))}
                                                  </span>
                                                <% end %>
                                                <%= if Map.has_key?(topic, :id) && Map.get(topic, :id) != nil && Map.get(topic, :id) > 0 do %>
                                                  <span class="badge badge-xs badge-accent">
                                                    ID:{Map.get(topic, :id)}
                                                  </span>
                                                <% end %>
                                                <div class="opacity-0 group-hover:opacity-100 transition-opacity flex gap-0.5 ml-1">
                                                  <button
                                                    phx-click="unsubscribe_topic"
                                                    phx-value-client_id={conn.client_id}
                                                    phx-value-topic={topic_str}
                                                    class="btn btn-xs btn-ghost text-error p-0 h-4 w-4 min-h-0"
                                                    title="Unsubscribe"
                                                  >
                                                    <.icon name="hero-x-mark" class="size-2.5" />
                                                  </button>
                                                </div>
                                              </div>
                                            <% topic_map when is_map(topic_map) -> %>
                                              <% # Extract topic string from either atom or string keys
                                              topic_str =
                                                cond do
                                                  Map.has_key?(topic_map, :topic) ->
                                                    Map.get(topic_map, :topic)

                                                  Map.has_key?(topic_map, "topic") ->
                                                    Map.get(topic_map, "topic")

                                                  true ->
                                                    "Unknown topic"
                                                end

                                              # Extract other properties with fallbacks for both atom and string keys
                                              qos = get_map_value(topic_map, :qos, "qos", 0)
                                              nl = get_map_value(topic_map, :nl, "nl", 0)
                                              rap = get_map_value(topic_map, :rap, "rap", 0)
                                              rh = get_map_value(topic_map, :rh, "rh", 0)
                                              sub_id = get_map_value(topic_map, :id, "id", nil) %>
                                              <div class="inline-flex items-center gap-1 bg-base-200 hover:bg-base-300 transition-colors rounded-md px-2 py-1 text-xs group">
                                                <span
                                                  class="font-medium max-w-24 truncate cursor-pointer hover:text-primary"
                                                  title={topic_str}
                                                  phx-click="edit_subscription"
                                                  phx-value-client_id={conn.client_id}
                                                  phx-value-topic={topic_str}
                                                  phx-value-qos={qos}
                                                  phx-value-nl={nl}
                                                  phx-value-rap={rap}
                                                  phx-value-rh={rh}
                                                  phx-value-sub_id={sub_id || ""}
                                                  phx-value-index={Map.get(topic_map, :index, "")}
                                                >
                                                  {topic_str}
                                                </span>
                                                <%= if qos != 0 do %>
                                                  <span class="badge badge-xs badge-primary">
                                                    {get_qos_badge(qos)}
                                                  </span>
                                                <% end %>
                                                <%= if nl == true or nl == 1 do %>
                                                  <span class="badge badge-xs badge-info">NL</span>
                                                <% end %>
                                                <%= if rap == true or rap == 1 do %>
                                                  <span class="badge badge-xs badge-warning">
                                                    RAP
                                                  </span>
                                                <% end %>
                                                <%= if rh != 0 do %>
                                                  <span class="badge badge-xs badge-secondary">
                                                    {get_rh_badge(rh)}
                                                  </span>
                                                <% end %>
                                                <%= if sub_id && sub_id > 0 do %>
                                                  <span class="badge badge-xs badge-accent">
                                                    ID:{sub_id}
                                                  </span>
                                                <% end %>
                                                <div class="opacity-0 group-hover:opacity-100 transition-opacity flex gap-0.5 ml-1">
                                                  <button
                                                    phx-click="unsubscribe_topic"
                                                    phx-value-client_id={conn.client_id}
                                                    phx-value-topic={topic_str}
                                                    class="btn btn-xs btn-ghost text-error p-0 h-4 w-4 min-h-0"
                                                    title="Unsubscribe"
                                                  >
                                                    <.icon name="hero-x-mark" class="size-2.5" />
                                                  </button>
                                                </div>
                                              </div>
                                            <% _ -> %>
                                              <div class="inline-flex items-center gap-1 bg-base-200 hover:bg-base-300 transition-colors rounded-md px-2 py-1 text-xs group">
                                                <span
                                                  class="font-medium max-w-24 truncate cursor-pointer hover:text-primary"
                                                  title={topic}
                                                  phx-click="edit_subscription"
                                                  phx-value-client_id={conn.client_id}
                                                  phx-value-topic={topic}
                                                  phx-value-qos={0}
                                                  phx-value-nl={false}
                                                  phx-value-rap={false}
                                                  phx-value-rh={0}
                                                  phx-value-index=""
                                                >
                                                  {topic}
                                                </span>
                                                <div class="opacity-0 group-hover:opacity-100 transition-opacity flex gap-0.5 ml-1">
                                                  <button
                                                    phx-click="unsubscribe_topic"
                                                    phx-value-client_id={conn.client_id}
                                                    phx-value-topic={topic}
                                                    class="btn btn-xs btn-ghost text-error p-0 h-4 w-4 min-h-0"
                                                    title="Unsubscribe"
                                                  >
                                                    <.icon name="hero-x-mark" class="size-2.5" />
                                                  </button>
                                                </div>
                                              </div>
                                          <% end %>
                                        <% end %>
                                      </div>
                                    <% else %>
                                      <span class="text-xs opacity-70">No subscriptions</span>
                                    <% end %>
                                  </div>
                                  <%= if conn.status == "connected" do %>
                                    <button
                                      phx-click="open_subscription_modal_for_client"
                                      phx-value-set_name={@active_connection_set.name}
                                      phx-value-client_id={conn.client_id}
                                      class="btn btn-xs btn-ghost text-primary"
                                      title="Add subscription"
                                    >
                                      <.icon name="hero-plus" class="size-3" />
                                    </button>
                                  <% end %>
                                </div>
                              </td>
                              
    <!-- Scheduled Messages -->
                              <td class="px-2 w-full">
                                <div class="flex justify-between items-start">
                                  <div class="flex-grow">
                                    <%= if Map.get(conn, :scheduled_messages, []) != [] do %>
                                      <div class="flex flex-wrap gap-1">
                                        <%= for {scheduled_msg, msg_index} <- Enum.with_index(Map.get(conn, :scheduled_messages, [])) do %>
                                          <div class="inline-flex items-center gap-1 bg-base-200 hover:bg-base-300 transition-colors rounded-md px-2 py-1 text-xs group">
                                            <span
                                              class="font-medium max-w-24 truncate cursor-pointer hover:text-primary"
                                              title={get_scheduled_msg_topic(scheduled_msg)}
                                              phx-click="edit_scheduled_message"
                                              phx-value-client_id={conn.client_id}
                                              phx-value-index={msg_index}
                                            >
                                              {get_scheduled_msg_topic(scheduled_msg)}
                                            </span>
                                            <span class="badge badge-xs badge-secondary">
                                              {format_interval(
                                                get_scheduled_msg_interval(scheduled_msg)
                                              )}
                                            </span>
                                            <%= if get_scheduled_msg_qos(scheduled_msg) != 0 do %>
                                              <span class="badge badge-xs badge-primary">
                                                {get_qos_badge(get_scheduled_msg_qos(scheduled_msg))}
                                              </span>
                                            <% end %>
                                            <%= if get_scheduled_msg_retain(scheduled_msg) do %>
                                              <span class="badge badge-xs badge-warning">R</span>
                                            <% end %>
                                            <div class="opacity-0 group-hover:opacity-100 transition-opacity flex gap-0.5 ml-1">
                                              <button
                                                phx-click="remove_scheduled_message"
                                                phx-value-client_id={conn.client_id}
                                                phx-value-index={msg_index}
                                                class="btn btn-xs btn-ghost text-error p-0 h-4 w-4 min-h-0"
                                                title="Remove scheduled message"
                                              >
                                                <.icon name="hero-x-mark" class="size-2.5" />
                                              </button>
                                            </div>
                                          </div>
                                        <% end %>
                                      </div>
                                    <% else %>
                                      <span class="text-xs opacity-70">No scheduled messages</span>
                                    <% end %>
                                  </div>
                                  <%= if conn.status == "connected" do %>
                                    <button
                                      phx-click="open_scheduled_message_modal_for_client"
                                      phx-value-set_name={@active_connection_set.name}
                                      phx-value-client_id={conn.client_id}
                                      class="btn btn-xs btn-ghost text-primary"
                                      title="Add scheduled message"
                                    >
                                      <.icon name="hero-plus" class="size-3" />
                                    </button>
                                  <% end %>
                                </div>
                              </td>
                              
    <!-- Connection Time -->
                              <td class="px-2 whitespace-nowrap">
                                <span class="text-xs">
                                  <%= if conn.connection_time do %>
                                    {format_datetime(conn.connection_time)}
                                  <% else %>
                                    -
                                  <% end %>
                                </span>
                              </td>
                            </tr>
                          <% end %>
                          <!-- Show "New Client" button in both semi and expanded states -->
                          <%= if @connection_table_state in ["semi", "expanded"] do %>
                            <tr class="table-row-hover">
                              <td colspan="7" class="px-0 py-1">
                                <button
                                  class="btn btn-ghost btn-xs w-full justify-start text-primary pl-3"
                                  phx-click="open_new_connection_modal"
                                  phx-value-name={Map.get(@active_connection_set, :name, "")}
                                >
                                  <.icon name="hero-plus-circle" class="h-4 w-4 mr-1" /> New Client
                                </button>
                              </td>
                            </tr>
                          <% end %>
                        </tbody>
                      </table>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      <% else %>
        <!-- Check if there are any brokers at all -->
        <%= if Enum.empty?(@connection_sets || []) do %>
          <!-- No brokers exist - show prominent call to action -->
          <div class="flex flex-col justify-center items-center h-64 bg-transparent rounded-box">
            <div class="text-center max-w-md">
              <!-- Icon -->
              <div class="mb-6">
                <.icon name="hero-server" class="w-16 h-16 mx-auto text-base-content/40" />
              </div>
              
    <!-- Title -->
              <h3 class="text-xl font-semibold text-base-content mb-3">
                No Brokers Available
              </h3>
              
    <!-- Description -->
              <p class="text-base-content/60 mb-6">
                Get started by creating your first MQTT broker connection.
                You can connect to local or remote MQTT brokers to start monitoring messages.
              </p>
              
    <!-- Call to Action Button -->
              <button
                type="button"
                class="btn btn-primary btn-lg"
                phx-click="open_connection_set_modal"
                phx-value-type="new_connection_set"
              >
                <.icon name="hero-plus" class="w-5 h-5 mr-2" /> Create Your First Broker
              </button>
            </div>
          </div>
        <% else %>
          <!-- Brokers exist but none selected -->
          <div class="flex justify-center items-center h-64 bg-transparent rounded-box">
            <div class="text-center">
              <.icon
                name="hero-cursor-arrow-rays"
                class="w-12 h-12 mx-auto text-base-content/40 mb-4"
              />
              <p class="text-base-content/60">Select a broker to view its details</p>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    # Get current values from socket
    current_active_connection_set = socket.assigns[:active_connection_set]
    current_expanded_sets = socket.assigns[:expanded_sets] || %{}

    # Check if any relevant data has changed
    active_set_changed = current_active_connection_set != assigns[:active_connection_set]
    new_expanded_sets = Map.get(assigns, :expanded_sets, %{})
    expanded_sets_changed = current_expanded_sets != new_expanded_sets

    # Always ensure active_connection_set is assigned to the socket
    socket = assign(socket, :active_connection_set, assigns[:active_connection_set])

    # Ensure expanded_sets is assigned
    socket = assign(socket, :expanded_sets, new_expanded_sets)

    # Update the socket with the new assigns if the active connection set has changed,
    # expanded_sets changed, or if we need to re-render due to status changes
    if active_set_changed or expanded_sets_changed do
      # Assign all the new values and group connections by status
      socket = assign_connections_by_status(socket, assigns)
      {:ok, socket}
    else
      # Check if any connection statuses have changed within the same active_connection_set
      if assigns[:active_connection_set] && socket.assigns[:active_connection_set] do
        old_connections = Map.get(socket.assigns[:active_connection_set], :connections, [])
        new_connections = Map.get(assigns[:active_connection_set], :connections, [])

        # Check if any connection status has changed
        status_changed = connection_status_changed?(old_connections, new_connections)

        if status_changed do
          # Re-assign connections by status
          socket = assign_connections_by_status(socket, assigns)
          {:ok, socket}
        else
          {:ok, socket}
        end
      else
        {:ok, socket}
      end
    end
  end

  @impl true
  def handle_event("set_connection_table_state", %{"state" => new_state}, socket) do
    # Validate the new state
    if new_state not in ["collapsed", "semi", "expanded"] do
      {:noreply, socket}
    else
      # Get the connection table key for this broker
      connection_table_key =
        if socket.assigns.active_connection_set do
          socket.assigns.active_connection_set.name
        else
          nil
        end

      # Update the expanded_sets in the server if we have a valid key
      if connection_table_key do
        # Get current expanded_sets from the server to ensure we have the latest state
        current_ui_state = Mqttable.ConnectionSets.get_ui_state()
        server_expanded_sets = Map.get(current_ui_state, :expanded_sets, %{})
        updated_expanded_sets = Map.put(server_expanded_sets, connection_table_key, new_state)

        # Save to server - this will trigger a PubSub broadcast that will update all clients
        Mqttable.ConnectionSets.update_expanded_sets(updated_expanded_sets)
      end

      # Don't update the local socket state here - let the PubSub broadcast handle it
      # This prevents the double-update issue where we update locally and then again via broadcast
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("open_edit_connection_set_modal", %{"name" => name}, socket) do
    # Send the event to the parent LiveView to open the edit modal
    send(self(), {:open_edit_connection_set_modal, %{"name" => name}})
    {:noreply, socket}
  end

  # Helper function to check if any connection status has changed
  defp connection_status_changed?(old_connections, new_connections) do
    # Create map of client_id -> status for easier comparison
    old_statuses =
      Enum.reduce(old_connections, %{}, fn conn, acc ->
        Map.put(acc, conn.client_id, Map.get(conn, :status))
      end)

    # Check if any status has changed
    Enum.any?(new_connections, fn conn ->
      client_id = conn.client_id
      old_status = Map.get(old_statuses, client_id)
      new_status = Map.get(conn, :status)
      old_status != new_status
    end)
  end

  # Helper function to assign connections grouped by status
  defp assign_connections_by_status(socket, assigns) do
    if assigns[:active_connection_set] do
      connections = Map.get(assigns[:active_connection_set], :connections, [])

      # Group connections by status
      connected = Enum.filter(connections, fn conn -> Map.get(conn, :status) == "connected" end)

      disconnected =
        Enum.filter(connections, fn conn -> Map.get(conn, :status) == "disconnected" end)

      reconnecting =
        Enum.filter(connections, fn conn -> Map.get(conn, :status) == "reconnecting" end)

      # Add connections without status to disconnected
      disconnected =
        disconnected ++ Enum.filter(connections, fn conn -> Map.get(conn, :status) == nil end)

      # Assign all values
      assign(
        socket,
        assigns
        |> Map.put(:connected_connections, connected)
        |> Map.put(:disconnected_connections, disconnected)
        |> Map.put(:reconnecting_connections, reconnecting)
      )
    else
      assign(
        socket,
        assigns
        |> Map.put(:connected_connections, [])
        |> Map.put(:disconnected_connections, [])
        |> Map.put(:reconnecting_connections, [])
      )
    end
  end

  # Helper function to generate a display name for a connection set
  def display_name(connection_set) do
    # Maximum length for display name
    max_length = 20

    cond do
      Map.has_key?(connection_set, :host) and Map.has_key?(connection_set, :port) and
          Map.has_key?(connection_set, :protocol) ->
        # Get the default port for this protocol
        protocol = connection_set.protocol
        default_port = Map.get(@default_ports, protocol)
        host = connection_set.host
        port = connection_set.port

        # Don't show port if it's the default for the protocol
        display =
          if port == default_port do
            host
          else
            "#{host}:#{port}"
          end

        # Limit to max_length characters
        if String.length(display) > max_length do
          String.slice(display, 0, max_length - 3) <> "..."
        else
          display
        end

      true ->
        "Unknown connection"
    end
  end

  # Helper function to generate a full connection URL for tooltip
  def connection_url(connection_set) do
    cond do
      Map.has_key?(connection_set, :protocol) and Map.has_key?(connection_set, :host) and
          Map.has_key?(connection_set, :port) ->
        "#{connection_set.protocol}://#{connection_set.host}:#{connection_set.port}"

      true ->
        "Unknown connection"
    end
  end

  # Helper function to convert color name to CSS background class
  def color_class(color) do
    case color do
      "blue" -> "bg-blue-500"
      "green" -> "bg-green-500"
      "red" -> "bg-red-500"
      "yellow" -> "bg-yellow-500"
      "purple" -> "bg-purple-500"
      _ -> "bg-blue-500"
    end
  end

  # Helper function to format datetime in RFC 3339 format
  def format_datetime(datetime) do
    time_zone = Mqttable.Settings.get_time_zone()

    case datetime do
      %DateTime{} ->
        datetime
        |> DateTime.shift_zone!(time_zone)
        |> Calendar.strftime("%Y-%m-%dT%H:%M:%S")

      %NaiveDateTime{} ->
        NaiveDateTime.to_string(datetime)

      datetime when is_binary(datetime) ->
        # Try to parse the string as a DateTime and then format it
        case DateTime.from_iso8601(datetime) do
          {:ok, dt, _offset} ->
            dt
            |> DateTime.shift_zone!(time_zone)
            |> Calendar.strftime("%Y-%m-%dT%H:%M:%S")

          # Return as is if parsing fails
          _error ->
            datetime
        end

      _ ->
        "-"
    end
  end

  # Helper function to get QoS badge text
  def get_qos_badge(qos) do
    case qos do
      0 -> "QoS0"
      1 -> "QoS1"
      2 -> "QoS2"
      _ -> "QoS?"
    end
  end

  # Helper function to get RH badge text
  def get_rh_badge(rh) do
    case rh do
      0 -> "RH0"
      1 -> "RH1"
      2 -> "RH2"
      _ -> "RH?"
    end
  end

  # Helper function to format MQTT version with equal-length labels and colors
  def format_mqtt_version(version) do
    case to_string(version) do
      "5" -> {"v5.0", "badge-accent"}
      "5.0" -> {"v5.0", "badge-accent"}
      "3" -> {"v3.0", "badge-accent"}
      "3.0" -> {"v3.0", "badge-accent"}
      "3.1" -> {"v3.1", "badge-accent"}
      "3.1.1" -> {"v3.1", "badge-accent"}
      # Default to v5.0
      _ -> {"v5.0", "badge-accent"}
    end
  end

  # Helper function to get a value from a map, trying both atom and string keys
  def get_map_value(map, atom_key, string_key, default) do
    cond do
      Map.has_key?(map, atom_key) -> Map.get(map, atom_key)
      Map.has_key?(map, string_key) -> Map.get(map, string_key)
      true -> default
    end
  end

  # Helper function to format interval in milliseconds to human-readable format
  def format_interval(interval_ms) when is_integer(interval_ms) do
    cond do
      interval_ms < 1000 -> "#{interval_ms}ms"
      interval_ms < 60_000 -> "#{div(interval_ms, 1000)}s"
      interval_ms < 3_600_000 -> "#{div(interval_ms, 60_000)}m"
      true -> "#{div(interval_ms, 3_600_000)}h"
    end
  end

  def format_interval(_), do: "?"

  # Helper functions to safely access scheduled message properties
  def get_scheduled_msg_topic(scheduled_msg) when is_map(scheduled_msg) do
    Map.get(scheduled_msg, :topic, Map.get(scheduled_msg, "topic", "Unknown"))
  end

  def get_scheduled_msg_topic(_), do: "Unknown"

  def get_scheduled_msg_interval(scheduled_msg) when is_map(scheduled_msg) do
    Map.get(scheduled_msg, :interval_ms, Map.get(scheduled_msg, "interval_ms", 5000))
  end

  def get_scheduled_msg_interval(_), do: 5000

  def get_scheduled_msg_qos(scheduled_msg) when is_map(scheduled_msg) do
    Map.get(scheduled_msg, :qos, Map.get(scheduled_msg, "qos", 0))
  end

  def get_scheduled_msg_qos(_), do: 0

  def get_scheduled_msg_retain(scheduled_msg) when is_map(scheduled_msg) do
    Map.get(scheduled_msg, :retain, Map.get(scheduled_msg, "retain", false))
  end

  def get_scheduled_msg_retain(_), do: false
end
